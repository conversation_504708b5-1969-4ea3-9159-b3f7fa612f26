use actlogica_logs::{
    builder::{LogAction, LogBuilder, create_log_span},
    log_error, log_info, log_warn,
    setting::{LogOutput, init_logger},
};
use futures::future;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tokio::{task, time};
use tracing::{Instrument, Span};
use tracing_subscriber::filter::LevelFilter;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();
    init_logger("Rust-log-collector", LevelFilter::INFO, LogOutput::StdOut)
        .await
        .unwrap();

    let span = create_log_span("main-module");
    let _enter = span.enter();
    log_info(LogBuilder::system("Logging before the thread"));

    let user_id = "123e4167-e89b-12d3-a416-426614174000";

    let task1 = {
        let user_id_clone = user_id.to_string();
        task::spawn(
            async move {
                let mut interval = time::interval(Duration::from_secs(3));
                let user_name = "rahul";
                loop {
                    interval.tick().await;
                    log_info(LogBuilder::user(
                        &user_id_clone,
                        user_name,
                        "User updated profile details",
                        Action::UserProfileUpdate,
                    ));
                }
            }
            .instrument(Span::current()),
        )
    };

    log_info(LogBuilder::system("Logging after the first thread"));
    St::print_event().await;

    let task2 = {
        let user_id_clone = user_id.to_string();
        let span = create_log_span("thread2-module");
        task::spawn(
            async move {
                let mut interval = time::interval(Duration::from_secs(3));
                let user_name = "rahul";
                loop {
                    interval.tick().await;
                    log_warn(LogBuilder::user(
                        &user_id_clone,
                        user_name,
                        "Multiple failed login attempts",
                        Action::SecurityWarning,
                    ));
                }
            }
            .instrument(span),
        )
    };

    log_info(LogBuilder::system("Logging after the second thread"));

    let task3 = {
        let user_id_clone = user_id.to_string();
        let span = create_log_span("thread3-module");
        task::spawn(
            async move {
                let mut interval = time::interval(Duration::from_secs(3));
                let user_name = "rahul";
                loop {
                    interval.tick().await;
                    log_error(LogBuilder::user(
                        &user_id_clone,
                        user_name,
                        "Transaction failed: Insufficient funds",
                        Action::TransactionFailed,
                    ));
                }
            }
            .instrument(span),
        )
    };

    // Test logs: Try make the framework & Vector confuse
    let task4 = task::spawn(async {
        let mut interval = time::interval(Duration::from_secs(3));
        loop {
            interval.tick().await;
            tracing::info!("Random Info Log msg just for testing");
            tracing::warn!("Random Warn Log msg just for testing");
            tracing::error!("Random Error Log msg just for testing");
        }
    });

    log_info(LogBuilder::system("Logging after the Third thread"));

    // Wait for all tasks
    let _ = future::join4(task1, task2, task3, task4).await;
}

#[derive(Debug, Serialize, Deserialize)]
pub(crate) enum Action {
    UserProfileUpdate,
    SecurityWarning,
    TransactionFailed,
}

impl LogAction for Action {}

impl ToString for Action {
    fn to_string(&self) -> String {
        match self {
            Action::UserProfileUpdate => "UserProfileUpdate".to_string(),
            Action::SecurityWarning => "SecurityWarning".to_string(),
            Action::TransactionFailed => "TransactionFailed".to_string(),
        }
    }
}

pub struct St;

impl St {
    pub async fn print_event() {
        log_info(LogBuilder::system("Logging from a struct"));
    }
}
