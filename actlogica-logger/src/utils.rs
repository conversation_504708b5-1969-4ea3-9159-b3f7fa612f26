use crate::setting::LogOutput;
use crate::setting::CustomFormatter;
use std::fs::{create_dir_all, OpenOptions};
use std::path::Path;
use std::sync::OnceLock;
use tracing_appender::non_blocking::{self, NonBlocking};

static LOG_GUARD: OnceLock<tracing_appender::non_blocking::WorkerGuard> = OnceLock::new();

pub fn setup_log_file(log_path: &str) -> NonBlocking {
    let log_dir = Path::new(log_path)
        .parent()
        .expect("Failed to determine log file directory");

    // Ensure the log directory exists
    if !log_dir.exists() {
        create_dir_all(log_dir).expect("Failed to create log directory");
    }

    // Open or create log file
    let log_file = OpenOptions::new()
        .create(true)
        .write(true)
        .append(false)
        .open(log_path)
        .expect("Failed to create log file");

    // Get non-blocking writer and store the guard
    let (non_blocking, guard) = non_blocking::NonBlockingBuilder::default()
        .buffered_lines_limit(100)
        .lossy(false)
        .finish(log_file);

    // Store the guard to prevent premature cleanup
    if LOG_GUARD.set(guard).is_err() {
        println!("Logger already initialized");
    }

    non_blocking
}

pub fn json_layer(
    service_name: &str,
    output_type: LogOutput,
) -> tracing_subscriber::fmt::Layer<tracing_subscriber::Registry, CustomFormatter> {
    tracing_subscriber::fmt::layer()
        .json()
        .without_time()
        .with_level(true)
        .with_current_span(true)
        .event_format(CustomFormatter {
            service_name: service_name.to_string(),
            output_type,
        })
}

pub fn human_layer() -> tracing_subscriber::fmt::Layer<tracing_subscriber::Registry, _> {
    tracing_subscriber::fmt::layer()
        .with_target(false)
        .with_level(true)
        .with_thread_ids(false)
        .with_thread_names(false)
}

